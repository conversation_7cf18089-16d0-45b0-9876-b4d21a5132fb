import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/title_with_logo.dart';
import 'package:portraitmode/appbar/title_without_logo.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

class PmAppBar extends ConsumerStatefulWidget implements PreferredSizeWidget {
  final bool primary;
  final bool automaticallyImplyLeading;
  final bool useBackButton;
  final Color? backButtonColor;
  final VoidCallback? onBackButtonPressed;
  final Widget? title;
  final String? titleText;
  final bool useLogo;
  final Color? backgroundColor;
  final double elevation;
  final bool elevateOnlyOnScroll;
  final double? height;
  final List<Widget>? actions;
  final Widget? flexibleSpace;
  final ScrollController? scrollController;

  const PmAppBar({
    super.key,
    this.primary = true,
    this.automaticallyImplyLeading = false,
    this.useBackButton = false,
    this.backButtonColor,
    this.onBackButtonPressed,
    this.title,
    this.titleText,
    this.useLogo = true,
    this.backgroundColor,
    this.elevation = 0.5,
    this.elevateOnlyOnScroll = false,
    // this.height = kToolbarHeight,
    this.height,
    this.actions,
    this.flexibleSpace,
    this.scrollController,
  });

  @override
  Size get preferredSize =>
      Size.fromHeight(height ?? (LayoutConfig.bottomNavBarHeight - 3.0));

  @override
  PmAppBarState createState() => PmAppBarState();
}

class PmAppBarState extends ConsumerState<PmAppBar> {
  late double _elevation;

  @override
  void initState() {
    super.initState();

    if (widget.scrollController != null && widget.elevateOnlyOnScroll) {
      _elevation = 0.0;
      widget.scrollController?.addListener(_updateElevation);
    } else {
      _elevation = widget.elevation;
    }
  }

  @override
  void dispose() {
    if (widget.scrollController != null && widget.elevateOnlyOnScroll) {
      widget.scrollController?.removeListener(_updateElevation);
    }

    super.dispose();
  }

  void _updateElevation() {
    if (widget.scrollController == null || !widget.elevateOnlyOnScroll) return;
    double newElevation = widget.scrollController!.offset > 0 ? 4.0 : 0;

    if (_elevation != newElevation) {
      setState(() {
        _elevation = newElevation;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      primary: widget.primary,
      automaticallyImplyLeading: widget.automaticallyImplyLeading,
      backgroundColor: widget.backgroundColor,
      elevation: _elevation,
      leading: widget.useBackButton
          ? BackButton(
              color: widget.backButtonColor,
              onPressed:
                  widget.onBackButtonPressed ?? () => Navigator.pop(context),
            )
          : null,
      title:
          widget.title ??
          (widget.useLogo || widget.titleText != null ? _buildTitle() : null),
      titleSpacing: widget.useBackButton
          ? 0.0
          : ScreenStyleConfig.horizontalPadding,
      actions:
          widget.actions ??
          [
            Padding(
              padding: const EdgeInsets.only(
                right: ScreenStyleConfig.horizontalPadding,
              ),
              child: ProfileAvatar(size: 32.0, toProfileScreen: true),
            ),
          ],
      flexibleSpace: widget.flexibleSpace,
    );
  }

  Widget _buildTitle() {
    return SizedBox(
      width: double.infinity,
      child: (widget.useLogo
          ? TitleWithLogo(title: widget.titleText)
          : TitleWithoutLogo(title: widget.titleText ?? '')),
    );
  }
}
