import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/dto/camera_photo_list_interaction_data.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_interaction_provider.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/load_more_builder.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CameraDetailScreenListMode extends ConsumerStatefulWidget {
  const CameraDetailScreenListMode({
    super.key,
    required this.camera,
    this.initialScrollIndex = 0,
  });

  final CameraData camera;
  final int initialScrollIndex;

  @override
  CameraDetailScreenListModeState createState() =>
      CameraDetailScreenListModeState();
}

class CameraDetailScreenListModeState
    extends ConsumerState<CameraDetailScreenListMode> {
  final _scrollController = ScrollController();
  final PhotoListService _photoListService = PhotoListService();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  bool _blockScrolling = false;
  late final int _profileId;

  late int _loadMorePerPage;
  bool _loadMoreEndReached = false;

  late final NotifierProvider<CameraPhotoListNotifier, List<PhotoData>>
  _cameraPhotoListProvider;

  late final NotifierProvider<
    CameraPhotoListInteractionNotifier,
    CameraPhotoListInteractionData
  >
  _interactionProvider;

  @override
  void initState() {
    _cameraPhotoListProvider = getCameraPhotoListProvider(widget.camera.slug);

    _interactionProvider = getCameraPhotoListInteractionProvider(
      widget.camera.slug,
    );

    _loadMorePerPage = LoadMoreConfig.camerasPerPage;
    _profileId = LocalUserService.userId ?? 0;

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: CameraDetailScreenListMode');
    const double appBarHeight = LayoutConfig.bottomNavBarHeight - 10;

    return Scaffold(
      appBar: PmAppBar(
        height: appBarHeight,
        titleText: widget.camera.name,
        useLogo: false,
        useBackButton: true,
        elevateOnlyOnScroll: true,
        scrollController: _scrollController,
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: _handleRefresh,
              child: _buildPositionedLoadMore(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPositionedLoadMore() {
    final photoList = ref.watch(_cameraPhotoListProvider);

    return ScrollablePositionedList.builder(
      physics: _blockScrolling ? const NeverScrollableScrollPhysics() : null,
      itemCount: photoList.length,
      itemBuilder: (BuildContext context, int index) {
        return LoadMoreBuilder(
          isFinished: _loadMoreEndReached,
          onLoadMore: _handleLoadMore,
          loadingWidgetColor: context.colors.baseColorAlt,
          idleStatusText: "",
          loadingStatusText: "",
          finishedStatusText: "",
          isLastIndex: (photoList.isEmpty || index == photoList.length - 1),
          child: _buildPhotoListItem(photoList[index], index),
        );
      },
      initialScrollIndex: widget.initialScrollIndex,
      itemScrollController: ItemScrollController(),
    );
  }

  Widget _buildPhotoListItem(PhotoData photo, int index) {
    double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
    bool isOwnProfile = photo.authorId == _profileId;

    return Container(
      margin: EdgeInsets.only(top: marginTop),
      child: PhotoListItem(
        index: index,
        photo: photo,
        isOwnProfile: isOwnProfile,
        screenName: 'camera_detail_screen',
        onTwoFingersOn: () {
          if (!mounted) return;

          setState(() {
            _blockScrolling = true;
          });
        },
        onTwoFingersOff: () {
          if (!mounted) return;

          setState(() {
            _blockScrolling = false;
          });
        },
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref.read(_interactionProvider.notifier).setLoadMoreLastId(0);
    _loadMoreEndReached = false;

    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: 0,
      cameraName: widget.camera.name,
    );

    _handlePhotosResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    final int loadMoreLastId = ref.read(_interactionProvider).loadMoreLastId;
    final isFirstLoad = loadMoreLastId == 0;

    PhotoListResponse response = await _photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      cameraName: widget.camera.name,
    );

    _handlePhotosResponse(response, false, isFirstLoad);

    return response.success;
  }

  void _handlePhotosResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(_interactionProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        if (isRefresh) {
          ref.read(_cameraPhotoListProvider.notifier).replaceAll(response.data);
        } else {
          if (isFirstLoad) {
            ref
                .read(_cameraPhotoListProvider.notifier)
                .replaceAll(response.data);
          } else {
            ref.read(_cameraPhotoListProvider.notifier).addItems(response.data);
          }
        }
      });
    }
  }
}
