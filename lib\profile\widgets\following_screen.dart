import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/services/following_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/home/<USER>/empty_following_notice.dart';
import 'package:portraitmode/max_width/max_width.dart';

class FollowingScreen extends ConsumerStatefulWidget {
  const FollowingScreen({super.key});

  @override
  FollowingScreenState createState() => FollowingScreenState();
}

class FollowingScreenState extends ConsumerState<FollowingScreen> {
  final _scrollController = ScrollController();

  int? _loadMoreLastId;
  int? _loadMoreLastTotalPhotos;
  bool _loadMoreEndReached = false;

  final FollowingService _followingService = FollowingService();
  final List<ArtistData> _artistList = [];

  bool _dataFetched = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEmpty = _dataFetched && _artistList.isEmpty;

    return Scaffold(
      appBar: isEmpty
          ? PmAppBar(
              titleText: "Following",
              elevation: 0.0,
              useBackButton: true,
              useLogo: false,
              actions: const [],
            )
          : null,
      body: MaxWidth(
        maxWidth: 768.0,
        child: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: isEmpty
              ? Center(
                  child: ListView(
                    shrinkWrap: true,
                    children: const [EmptyFollowingNotice()],
                  ),
                )
              : CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    PmSliverAppBar(
                      scrollController: _scrollController,
                      titleText: "Following",
                      automaticallyImplyLeading: true,
                      useLogo: false,
                      actions: const [],
                    ),
                    EasyLoadMore(
                      isFinished: _loadMoreEndReached,
                      onLoadMore: _handleLoadMore,
                      loadingWidgetColor: context.colors.baseColorAlt,
                      runOnEmptyResult: true,
                      idleStatusText: "",
                      loadingStatusText: "",
                      finishedStatusText: "",
                      child: _buildSliverList(),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildSliverList() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;

        return Padding(
          padding: EdgeInsets.only(
            left: ScreenStyleConfig.horizontalPadding,
            right: ScreenStyleConfig.horizontalPadding,
            top: marginTop,
          ),
          child: ArtistListItem(
            index: index,
            artist: _artistList[index],
            isFollowingScreen: true,
          ),
        );
      }, childCount: _artistList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _artistList.clear();

    _loadMoreLastId = null;
    _loadMoreLastTotalPhotos = null;

    await _fetchArtists();

    _loadMoreEndReached = false;
  }

  Future<bool> _handleLoadMore() async {
    await _fetchArtists(
      lastId: _loadMoreLastId,
      lastTotalPhotos: _loadMoreLastTotalPhotos,
    );

    return true;
  }

  Future<void> _fetchArtists({
    int limit = 20,
    int? lastId,
    int? lastTotalPhotos,
  }) async {
    ArtistListResponse response = await _followingService.fetch(
      limit: limit,
      lastId: lastId,
      lastTotalPhotos: lastTotalPhotos,
    );

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
          _dataFetched = true;
        });
      }

      return;
    }

    _loadMoreLastId = response.data.last.id;
    _loadMoreLastTotalPhotos = response.data.last.totalPhotos;

    if (mounted) {
      setState(() {
        _dataFetched = true;
        _artistList.addAll(response.data);
      });
    }
  }
}
